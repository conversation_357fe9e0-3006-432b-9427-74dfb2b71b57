{"name": "benchmark", "version": "0.0.1", "description": "OCR Benchmark", "main": "index.js", "scripts": {"build": "tsc", "test": "jest", "benchmark": "ts-node src/index.ts"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/azure": "^1.1.9", "@ai-sdk/deepseek": "^0.1.6", "@ai-sdk/google": "^1.1.10", "@ai-sdk/openai": "^1.3.14", "@aws-sdk/client-textract": "^3.716.0", "@azure-rest/ai-document-intelligence": "^1.0.0", "@azure/core-auth": "^1.9.0", "@google-cloud/documentai": "^8.12.0", "@google/generative-ai": "^0.21.0", "@huggingface/hub": "^1.0.1", "@mistralai/mistralai": "^1.5.1", "@prisma/client": "^6.3.1", "ai": "^4.3.16", "axios": "^1.7.9", "canvas": "^3.1.0", "cli-progress": "^3.12.0", "dotenv": "^16.4.7", "fastest-levenshtein": "^1.0.16", "form-data": "^4.0.2", "jimp": "^1.6.0", "json-diff": "^1.0.6", "lodash": "^4.17.21", "moment": "^2.30.1", "openai": "^4.94.0", "p-limit": "^3.1.0", "pdfkit": "^0.17.1", "pg": "^8.13.1", "sharp": "^0.33.5", "together-ai": "^0.13.0", "turndown": "^7.2.0", "zerox": "^1.0.43"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/jest": "^29.5.14", "eslint": "^9.17.0", "jest": "^29.7.0", "prettier": "^3.4.2", "prisma": "^6.3.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1"}, "keywords": ["OCR", "Benchmark", "LLM"], "author": "[@annapo23, @tyler<PERSON>n, @kailingding, @zeeshan]", "license": "ISC"}